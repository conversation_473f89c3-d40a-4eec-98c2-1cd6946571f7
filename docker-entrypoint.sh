#!/bin/bash
set -e

# Docker entrypoint script for telegram-mcp-client

echo "🚀 Starting Telegram MCP Client..."

# Create necessary directories
mkdir -p /app/.tmp
mkdir -p /app/logs

# Check if required environment variables are set
required_vars=("TELEGRAM_TOKEN" "ANTHROPIC_API_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Error: Missing required environment variables:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo ""
    echo "Please set these variables in your .env file or docker-compose.yml"
    exit 1
fi

# Validate Python environment
echo "🐍 Checking Python environment..."
python --version
pip --version

# Check if all required packages are installed
echo "📦 Checking Python dependencies..."
python -c "
import sys
try:
    import agno
    import anthropic
    import telegram
    import mcp
    print('✅ All core dependencies are available')
except ImportError as e:
    print(f'❌ Missing dependency: {e}')
    sys.exit(1)
"

# Check Node.js for MCP tools
echo "📦 Checking Node.js for MCP tools..."
node --version
npm --version

# Pre-install commonly used MCP packages to avoid runtime delays
echo "🔧 Pre-installing MCP packages..."
npm install -g todoist-mcp || echo "⚠️  Warning: Could not pre-install todoist-mcp"

# Set up proper signal handling
trap 'echo "🛑 Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Log startup information
echo "📋 Startup Information:"
echo "   - Python: $(python --version)"
echo "   - Node.js: $(node --version)"
echo "   - Working Directory: $(pwd)"
echo "   - User: $(whoami)"
echo "   - Database Path: /app/.tmp/agent.db"
echo "   - Environment: ${ENVIRONMENT:-production}"

# Check database directory permissions
if [ ! -w "/app/.tmp" ]; then
    echo "❌ Error: Cannot write to database directory /app/.tmp"
    exit 1
fi

echo "✅ All checks passed, starting application..."
echo ""

# Execute the main command
exec "$@"
