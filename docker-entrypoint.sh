#!/bin/bash
set -e

# Docker entrypoint script for telegram-mcp-client

echo "🚀 Starting Telegram MCP Client..."

# Create necessary directories (running as root, no permission issues)
mkdir -p /app/.tmp /app/logs
chmod 755 /app/.tmp /app/logs

# Check if required environment variables are set
required_vars=("TELEGRAM_TOKEN" "ANTHROPIC_API_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Error: Missing required environment variables:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo ""
    echo "Please set these variables in your .env file or docker compose.yml"
    exit 1
fi

# Validate Python environment
echo "🐍 Checking Python environment..."
python --version
pip --version

# Check if all required packages are installed
echo "📦 Checking Python dependencies..."
python -c "
import sys
try:
    import agno
    import anthropic
    import telegram
    import mcp
    print('✅ All core dependencies are available')
except ImportError as e:
    print(f'❌ Missing dependency: {e}')
    sys.exit(1)
"

# Check Node.js for MCP tools
echo "📦 Checking Node.js and npm for MCP tools..."
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"

# Check if npm packages are available
echo "🔧 Checking MCP packages..."
npm list -g --depth=0 2>/dev/null | grep -E "(todoist-mcp|server-brave-search|server-fetch)" || echo "   Some MCP packages may need runtime installation"

# Set up npm cache (running as root, no issues)
export NPM_CONFIG_CACHE=/root/.npm
mkdir -p /root/.npm

# Set up proper signal handling
trap 'echo "🛑 Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Log startup information
echo "📋 Startup Information:"
echo "   - Python: $(python --version)"
echo "   - Node.js: $(node --version)"
echo "   - Working Directory: $(pwd)"
echo "   - User: $(whoami)"
echo "   - Database Path: /app/.tmp/agent.db"
echo "   - Environment: ${ENVIRONMENT:-production}"

# Check database directory permissions
if [ ! -w "/app/.tmp" ]; then
    echo "❌ Error: Cannot write to database directory /app/.tmp"
    echo "   Directory permissions: $(ls -ld /app/.tmp)"
    echo "   Current user: $(whoami)"
    echo "   User ID: $(id)"
    exit 1
fi

# Check logs directory permissions
if [ ! -w "/app/logs" ]; then
    echo "⚠️  Warning: Cannot write to logs directory /app/logs"
    echo "   Directory permissions: $(ls -ld /app/logs)"
fi

echo "✅ All checks passed, starting application..."
echo ""

# Execute the main command
exec "$@"
