#!/bin/bash

# Quick Start Script for Telegram MCP Client
# This script helps you get up and running quickly with Docker

set -e

echo "🚀 Telegram MCP Client - Quick Start"
echo "===================================="
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first:"
    echo "   https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first:"
    echo "   https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Setting up environment configuration..."
    cp docker.env.example .env
    echo "✅ Created .env file from template"
    echo ""
    echo "⚠️  IMPORTANT: You need to edit .env with your API keys!"
    echo ""
    echo "Required API keys:"
    echo "  - TELEGRAM_TOKEN (get from @BotFather on Telegram)"
    echo "  - ANTHROPIC_API_KEY (get from https://console.anthropic.com/)"
    echo ""
    echo "Optional API keys (for enhanced features):"
    echo "  - OPENAI_API_KEY (for memory management)"
    echo "  - OPENROUTER_API_KEY (for session summarization)"
    echo "  - API_KEY (Todoist API for task management)"
    echo ""
    
    read -p "Would you like to edit .env now? (y/n): " edit_env
    if [ "$edit_env" = "y" ] || [ "$edit_env" = "Y" ]; then
        if command -v nano &> /dev/null; then
            nano .env
        elif command -v vim &> /dev/null; then
            vim .env
        elif command -v code &> /dev/null; then
            code .env
        else
            echo "Please edit .env manually with your preferred editor"
            exit 1
        fi
    else
        echo "Please edit .env manually before continuing"
        exit 1
    fi
else
    echo "✅ .env file already exists"
fi

echo ""
echo "🔍 Checking environment configuration..."

# Check if required variables are set
if ! grep -q "TELEGRAM_TOKEN=.*[^[:space:]]" .env || grep -q "TELEGRAM_TOKEN=your_telegram_bot_token_here" .env; then
    echo "❌ TELEGRAM_TOKEN is not set in .env"
    echo "Please edit .env and set your Telegram bot token"
    exit 1
fi

if ! grep -q "ANTHROPIC_API_KEY=.*[^[:space:]]" .env || grep -q "ANTHROPIC_API_KEY=your_anthropic_api_key_here" .env; then
    echo "❌ ANTHROPIC_API_KEY is not set in .env"
    echo "Please edit .env and set your Anthropic API key"
    exit 1
fi

echo "✅ Required environment variables are configured"
echo ""

# Create data directories
echo "📁 Creating data directories..."
mkdir -p data logs backups
echo "✅ Data directories created"
echo ""

# Build and start the application
echo "🏗️  Building Docker image..."
docker-compose build

echo ""
echo "🚀 Starting the application..."
docker-compose up -d

echo ""
echo "⏳ Waiting for application to start..."
sleep 10

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Application is running!"
    echo ""
    echo "📋 Quick Commands:"
    echo "  View logs:     docker-compose logs -f"
    echo "  Stop app:      docker-compose down"
    echo "  Restart app:   docker-compose restart"
    echo "  Check status:  docker-compose ps"
    echo ""
    echo "📖 For more commands, see DOCKER.md or run 'make help'"
    echo ""
    echo "🎉 Your Telegram bot should now be online!"
    echo "   Send a message to your bot to test it."
else
    echo "❌ Application failed to start. Check logs:"
    docker-compose logs
    exit 1
fi
