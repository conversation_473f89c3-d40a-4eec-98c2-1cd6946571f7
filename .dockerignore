# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
*.md
docs/

# Temporary files
.tmp/
*.log
*.useful

# Development files
.claude/
ideas.md
update-claude.md
telegram-client-example.txt

# Data directories (will be mounted as volumes)
data/
logs/

# Environment files (will be handled separately)
.env*
!.env.example

# Cache directories
.cache/
.pytest_cache/
