# Docker Setup for Telegram MCP Client

This document provides comprehensive instructions for building, running, and deploying the Telegram MCP Client using Docker.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+ 
- Docker Compose 2.0+
- At least 1GB of available RAM
- Your API keys ready (Telegram, Anthropic, etc.)

### 1. <PERSON>lone and Setup

```bash
git clone <your-repo-url>
cd telegram-mcp-client
```

### 2. Configure Environment

```bash
# Copy the Docker environment template
cp docker.env.example .env

# Edit .env with your actual API keys
nano .env  # or use your preferred editor
```

**Required variables:**
- `TELEGRAM_TOKEN` - Get from [@BotFather](https://t.me/BotFather)
- `ANTHROPIC_API_KEY` - Get from [Anthropic Console](https://console.anthropic.com/)

### 3. Build and Run

```bash
# Build and start the container
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the container
docker-compose down
```

## 📁 Project Structure

```
telegram-mcp-client/
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Production deployment
├── docker-compose.dev.yml     # Development overrides
├── docker-entrypoint.sh       # Container startup script
├── .dockerignore              # Files to exclude from build
├── docker.env.example         # Environment template
├── data/                      # Persistent data (auto-created)
│   └── agent.db              # SQLite database
└── logs/                      # Application logs (auto-created)
```

## 🔧 Configuration

### Environment Variables

The application uses environment variables for configuration. Copy `docker.env.example` to `.env` and configure:

#### Required
- `TELEGRAM_TOKEN` - Your Telegram bot token
- `ANTHROPIC_API_KEY` - Your Anthropic API key for Claude

#### Optional
- `OPENAI_API_KEY` - For memory management features
- `OPENROUTER_API_KEY` - For session summarization
- `DEEPGRAM_API_KEY` - For speech-to-text functionality
- `API_KEY` - Todoist API key for task management
- `CLAUDE_MODEL` - Claude model to use (default: claude-3-5-sonnet-latest)

### Volume Mounts

The Docker setup includes persistent storage:

- `./data:/app/.tmp` - SQLite database and session storage
- `./logs:/app/logs` - Application logs (optional)

## 🏗️ Build Details

### Multi-Stage Build

The Dockerfile uses a multi-stage build for optimization:

1. **Builder Stage**: Installs build dependencies and Python packages
2. **Runtime Stage**: Creates minimal runtime environment

### Features

- **Python 3.12** runtime
- **Node.js 20** for MCP tools
- **Non-root user** for security
- **Health checks** for monitoring
- **Signal handling** for graceful shutdown
- **Resource limits** for production use

## 🚀 Deployment Options

### Production Deployment

```bash
# Standard production deployment
docker-compose up -d

# With custom resource limits
docker-compose up -d --scale telegram-mcp-client=1
```

### Development Mode

For development with live code reloading:

```bash
# Start in development mode
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Or with rebuild
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build
```

Development mode includes:
- Source code mounted as volume
- Debug logging enabled
- No resource limits
- Interactive terminal access

## 🔍 Monitoring and Troubleshooting

### View Logs

```bash
# Follow all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f telegram-mcp-client

# View last 100 lines
docker-compose logs --tail=100 telegram-mcp-client
```

### Health Checks

The container includes health checks:

```bash
# Check container health
docker-compose ps

# Manual health check
docker exec telegram-mcp-client python -c "import sys; sys.exit(0)"
```

### Common Issues

#### 1. Permission Errors
```bash
# Fix data directory permissions
sudo chown -R $USER:$USER ./data ./logs
```

#### 2. Missing Environment Variables
```bash
# Check environment variables
docker-compose config
```

#### 3. Database Issues
```bash
# Reset database (⚠️ This will delete all data)
docker-compose down
rm -rf ./data
docker-compose up -d
```

### Debug Mode

For debugging issues:

```bash
# Run container interactively
docker-compose run --rm telegram-mcp-client bash

# Check Python environment
docker-compose exec telegram-mcp-client python -c "import agno; print('OK')"

# Check Node.js environment
docker-compose exec telegram-mcp-client node --version
```

## 🔒 Security Considerations

### Best Practices Implemented

1. **Non-root user**: Container runs as `appuser`
2. **Minimal base image**: Uses Python slim image
3. **No secrets in image**: All sensitive data via environment variables
4. **Resource limits**: Prevents resource exhaustion
5. **Health checks**: Enables monitoring

### Additional Security

For production deployment:

```bash
# Use Docker secrets (recommended)
echo "your_telegram_token" | docker secret create telegram_token -

# Or use external secret management
# - HashiCorp Vault
# - AWS Secrets Manager
# - Azure Key Vault
```

## 📊 Resource Requirements

### Minimum Requirements
- **RAM**: 512MB
- **CPU**: 0.25 cores
- **Storage**: 100MB + database growth

### Recommended for Production
- **RAM**: 1GB
- **CPU**: 0.5 cores  
- **Storage**: 1GB + database growth

### Scaling

For high-traffic scenarios:

```bash
# Increase resource limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
```

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Backups

```bash
# Backup database
docker-compose exec telegram-mcp-client cp /app/.tmp/agent.db /app/backup.db
docker cp telegram-mcp-client:/app/backup.db ./backup-$(date +%Y%m%d).db

# Restore database
docker cp ./backup-20240101.db telegram-mcp-client:/app/.tmp/agent.db
docker-compose restart
```

## 🆘 Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify environment variables: `docker-compose config`
3. Test health check: `docker-compose ps`
4. Review this documentation
5. Check the main project README.md

For development questions, refer to the main project documentation in `ARCHITECTURE.md`.
