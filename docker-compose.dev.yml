# Development override for docker compose
# Usage: docker compose -f docker compose.yml -f docker compose.dev.yml up

version: '3.8'

services:
  telegram-mcp-client:
    # Mount source code for live development
    volumes:
      - .:/app
      - ./data:/app/.tmp
      - ./logs:/app/logs
      # Exclude node_modules and Python cache from host
      - /app/node_modules
      - /app/__pycache__
    
    # Enable debug mode and development settings
    environment:
      - PYTHONUNBUFFERED=1
      - AGNO_TELEMETRY=false
      - DEBUG=true
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
    
    # Override command for development with auto-reload
    command: ["python", "-u", "main.py"]
    
    # Remove resource limits for development
    deploy: {}
    
    # Enable stdin for interactive debugging
    stdin_open: true
    tty: true
    
    # Override healthcheck for faster development cycles
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 60s
      timeout: 10s
      retries: 2
      start_period: 10s
