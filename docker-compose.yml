version: '3.8'

services:
  telegram-mcp-client:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: telegram-mcp-client
    restart: unless-stopped
    
    # Environment variables
    env_file:
      - .env
    
    # Additional environment variables for Docker
    environment:
      - PYTHONUNBUFFERED=1
      - AGNO_TELEMETRY=false
    
    # Volume mounts for data persistence
    volumes:
      # Persist SQLite database and session data
      - ./data:/app/.tmp
      # Optional: Mount logs directory
      - ./logs:/app/logs
    
    # Network configuration
    networks:
      - telegram-bot-network
    
    # Resource limits (adjust as needed)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  telegram-bot-network:
    driver: bridge

# Optional: Add a development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml (development overrides)
version: '3.8'

services:
  telegram-mcp-client:
    # Mount source code for development
    volumes:
      - .:/app
      - ./data:/app/.tmp
      - ./logs:/app/logs
    
    # Enable debug mode
    environment:
      - PYTHONUNBUFFERED=1
      - AGNO_TELEMETRY=false
      - DEBUG=true
    
    # Override command for development
    command: ["python", "-u", "main.py"]
    
    # Remove resource limits for development
    deploy: {}
