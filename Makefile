# Makefile for Telegram MCP Client Docker operations
# Makes Docker operations easier for single-user deployment

.PHONY: help build up down logs restart clean dev setup backup restore

# Default target
help: ## Show this help message
	@echo "Telegram MCP Client - Docker Commands"
	@echo "====================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Setup and configuration
setup: ## Setup environment file from template
	@if [ ! -f .env ]; then \
		cp docker.env.example .env; \
		echo "✅ Created .env file from template"; \
		echo "⚠️  Please edit .env with your API keys before running 'make up'"; \
	else \
		echo "⚠️  .env file already exists"; \
	fi

# Build operations
build: ## Build the Docker image
	docker-compose build

build-no-cache: ## Build the Docker image without cache
	docker-compose build --no-cache

# Runtime operations
up: ## Start the application in production mode
	@if [ ! -f .env ]; then \
		echo "❌ .env file not found. Run 'make setup' first."; \
		exit 1; \
	fi
	docker-compose up -d
	@echo "✅ Application started. Use 'make logs' to view output."

down: ## Stop the application
	docker-compose down

restart: ## Restart the application
	docker-compose restart

# Development operations
dev: ## Start in development mode with live reload
	@if [ ! -f .env ]; then \
		echo "❌ .env file not found. Run 'make setup' first."; \
		exit 1; \
	fi
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

dev-build: ## Build and start in development mode
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Monitoring operations
logs: ## View application logs
	docker-compose logs -f

logs-tail: ## View last 100 lines of logs
	docker-compose logs --tail=100

status: ## Show container status
	docker-compose ps

health: ## Check application health
	docker-compose exec telegram-mcp-client python -c "import sys; print('✅ Application is healthy'); sys.exit(0)" || echo "❌ Application health check failed"

# Maintenance operations
clean: ## Remove containers, networks, and images
	docker-compose down --rmi all --volumes --remove-orphans

clean-data: ## ⚠️ Remove all data (database and logs) - DESTRUCTIVE!
	@echo "⚠️  This will delete ALL application data including database and logs!"
	@read -p "Are you sure? Type 'yes' to continue: " confirm; \
	if [ "$$confirm" = "yes" ]; then \
		docker-compose down; \
		sudo rm -rf ./data ./logs; \
		echo "✅ All data removed"; \
	else \
		echo "❌ Operation cancelled"; \
	fi

# Backup operations
backup: ## Create database backup
	@mkdir -p ./backups
	@backup_name="backup-$$(date +%Y%m%d-%H%M%S).db"; \
	docker-compose exec telegram-mcp-client cp /app/.tmp/agent.db /app/$$backup_name; \
	docker cp telegram-mcp-client:/app/$$backup_name ./backups/$$backup_name; \
	docker-compose exec telegram-mcp-client rm /app/$$backup_name; \
	echo "✅ Database backed up to ./backups/$$backup_name"

restore: ## Restore database from backup (usage: make restore BACKUP=backup-20240101-120000.db)
	@if [ -z "$(BACKUP)" ]; then \
		echo "❌ Please specify backup file: make restore BACKUP=backup-20240101-120000.db"; \
		ls -la ./backups/ 2>/dev/null || echo "No backups found"; \
		exit 1; \
	fi
	@if [ ! -f "./backups/$(BACKUP)" ]; then \
		echo "❌ Backup file ./backups/$(BACKUP) not found"; \
		exit 1; \
	fi
	docker-compose down
	docker cp ./backups/$(BACKUP) telegram-mcp-client:/app/.tmp/agent.db || true
	docker-compose up -d
	@echo "✅ Database restored from $(BACKUP)"

# Debug operations
shell: ## Open shell in running container
	docker-compose exec telegram-mcp-client bash

debug: ## Run container interactively for debugging
	docker-compose run --rm telegram-mcp-client bash

# Update operations
update: ## Update application (git pull + rebuild)
	git pull
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
	@echo "✅ Application updated and restarted"

# Quick commands for daily use
start: up ## Alias for 'up'
stop: down ## Alias for 'down'
